body {
    margin: 0;
    padding-top: 55px;
    overflow-y: scroll;
    font-family: Lato, 'Helvetica Neue', Arial, Helvetica, "Microsoft YaHei", sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scrollbar-width: none;
    color: var(--semi-color-text-0) !important;
    background-color: var( --semi-color-bg-0) !important;
    height: 100%;
}

#root {
    height: 100%;
}

@media only screen and (max-width: 767px) {
    .semi-table-tbody, .semi-table-row, .semi-table-row-cell {
        display: block!important;
        width: auto!important;
        padding: 2px!important;
    }
    .semi-table-row-cell {
        border-bottom: 0!important;
    }
    .semi-table-tbody>.semi-table-row {
        border-bottom: 1px solid rgba(0,0,0,.1);
    }
    .semi-space {
        /*display: block!important;*/
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        row-gap: 3px;
        column-gap: 10px;
    }
}

.semi-table-tbody > .semi-table-row > .semi-table-row-cell {
    padding: 16px 14px;
}

.channel-table {
    .semi-table-tbody > .semi-table-row > .semi-table-row-cell {
        padding: 16px 8px;
    }
}

.semi-layout {
    height: 100%;
}

.tableShow {
    display: revert;
}

.tableHiddle {
    display: none !important;
}

body::-webkit-scrollbar {
    display: none;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

.semi-navigation-vertical {
    /*display: flex;*/
    /*flex-direction: column;*/
}

.semi-navigation-item {
    margin-bottom: 0;
}

.semi-navigation-vertical {
    /*flex: 0 0 auto;*/
    /*display: flex;*/
    /*flex-direction: column;*/
    /*width: 100%;*/
    height: 100%;
    overflow: hidden;
}

.main-content {
    padding: 4px;
    height: 100%;
}

.small-icon .icon {
    font-size: 1em !important;
}

.custom-footer {
    font-size: 1.1em;
}

@media only screen and (max-width: 600px) {
    .hide-on-mobile {
        display: none !important;
    }
}


/* 隐藏浏览器默认的滚动条 */
body {
    overflow: hidden;
}

/* 自定义滚动条样式 */
body::-webkit-scrollbar {
    width: 0;  /* 隐藏滚动条的宽度 */
}

/* Dark mode enhancements for Semi-UI components */
[theme-mode="dark"] .semi-input,
[theme-mode="dark"] .semi-input-wrapper,
[theme-mode="dark"] .semi-textarea-wrapper {
    background-color: var(--semi-color-fill-0) !important;
    border-color: var(--semi-color-border) !important;
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-input:focus,
[theme-mode="dark"] .semi-input-wrapper:focus-within,
[theme-mode="dark"] .semi-textarea-wrapper:focus-within {
    border-color: var(--semi-color-primary) !important;
    background-color: var(--semi-color-fill-0) !important;
}

[theme-mode="dark"] .semi-textarea {
    background-color: var(--semi-color-fill-0) !important;
    color: var(--semi-color-text-0) !important;
    border-color: var(--semi-color-border) !important;
}

[theme-mode="dark"] .semi-textarea:focus {
    border-color: var(--semi-color-primary) !important;
    background-color: var(--semi-color-fill-0) !important;
}

[theme-mode="dark"] .semi-select,
[theme-mode="dark"] .semi-select-selection {
    background-color: var(--semi-color-fill-0) !important;
    border-color: var(--semi-color-border) !important;
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-select:focus,
[theme-mode="dark"] .semi-select-selection:focus {
    border-color: var(--semi-color-primary) !important;
}

[theme-mode="dark"] .semi-select-option-list {
    background-color: var(--semi-color-bg-2) !important;
    border-color: var(--semi-color-border) !important;
}

[theme-mode="dark"] .semi-select-option {
    background-color: var(--semi-color-bg-2) !important;
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-select-option:hover {
    background-color: var(--semi-color-fill-1) !important;
}

[theme-mode="dark"] .semi-form-field-label {
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-tabs-content {
    background-color: var(--semi-color-bg-1) !important;
}

[theme-mode="dark"] .semi-tabs-tab {
    color: var(--semi-color-text-1) !important;
}

[theme-mode="dark"] .semi-tabs-tab-active {
    color: var(--semi-color-text-0) !important;
}

/* Code blocks and pre elements for dark mode */
[theme-mode="dark"] pre {
    background-color: var(--semi-color-fill-0) !important;
    color: var(--semi-color-text-0) !important;
    border: 1px solid var(--semi-color-border) !important;
}

[theme-mode="dark"] code {
    background-color: var(--semi-color-fill-0) !important;
    color: var(--semi-color-text-0) !important;
}

/* Message and notification components */
[theme-mode="dark"] .semi-notification {
    background-color: var(--semi-color-bg-2) !important;
    border-color: var(--semi-color-border) !important;
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-toast {
    background-color: var(--semi-color-bg-2) !important;
    color: var(--semi-color-text-0) !important;
}

/* Ensure all generic inputs have dark styling */
[theme-mode="dark"] input,
[theme-mode="dark"] textarea,
[theme-mode="dark"] select {
    background-color: var(--semi-color-fill-0) !important;
    color: var(--semi-color-text-0) !important;
    border: 1px solid var(--semi-color-border) !important;
}

[theme-mode="dark"] input:focus,
[theme-mode="dark"] textarea:focus,
[theme-mode="dark"] select:focus {
    border-color: var(--semi-color-primary) !important;
    background-color: var(--semi-color-fill-0) !important;
    outline: none !important;
}

/* Custom scrollbar for dark mode */
[theme-mode="dark"] *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[theme-mode="dark"] *::-webkit-scrollbar-track {
    background: var(--semi-color-fill-0);
}

[theme-mode="dark"] *::-webkit-scrollbar-thumb {
    background-color: var(--semi-color-border);
    border-radius: 4px;
}

[theme-mode="dark"] *::-webkit-scrollbar-thumb:hover {
    background-color: var(--semi-color-text-2);
}

/* Dark mode enhancements for Semi-UI Tag components */
[theme-mode="dark"] .semi-tag {
    background-color: rgba(255, 255, 255, 0.08) !important;
    color: var(--semi-color-text-0) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

/* Color-specific tag styles for dark mode */
[theme-mode="dark"] .semi-tag-amber {
    background-color: rgba(255, 193, 7, 0.15) !important;
    color: #ffc947 !important;
    border: 1px solid rgba(255, 193, 7, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-blue {
    background-color: rgba(33, 150, 243, 0.15) !important;
    color: #64b5f6 !important;
    border: 1px solid rgba(33, 150, 243, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-cyan {
    background-color: rgba(0, 188, 212, 0.15) !important;
    color: #4dd0e1 !important;
    border: 1px solid rgba(0, 188, 212, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-green {
    background-color: rgba(76, 175, 80, 0.15) !important;
    color: #81c784 !important;
    border: 1px solid rgba(76, 175, 80, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-grey {
    background-color: rgba(158, 158, 158, 0.15) !important;
    color: #bdbdbd !important;
    border: 1px solid rgba(158, 158, 158, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-indigo {
    background-color: rgba(63, 81, 181, 0.15) !important;
    color: #7986cb !important;
    border: 1px solid rgba(63, 81, 181, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-light-blue {
    background-color: rgba(3, 169, 244, 0.15) !important;
    color: #4fc3f7 !important;
    border: 1px solid rgba(3, 169, 244, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-lime {
    background-color: rgba(205, 220, 57, 0.15) !important;
    color: #dce775 !important;
    border: 1px solid rgba(205, 220, 57, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-orange {
    background-color: rgba(255, 152, 0, 0.15) !important;
    color: #ffb74d !important;
    border: 1px solid rgba(255, 152, 0, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-pink {
    background-color: rgba(233, 30, 99, 0.15) !important;
    color: #f06292 !important;
    border: 1px solid rgba(233, 30, 99, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-purple {
    background-color: rgba(156, 39, 176, 0.15) !important;
    color: #ba68c8 !important;
    border: 1px solid rgba(156, 39, 176, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-red {
    background-color: rgba(244, 67, 54, 0.15) !important;
    color: #ff8a80 !important;
    border: 1px solid rgba(244, 67, 54, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-teal {
    background-color: rgba(0, 150, 136, 0.15) !important;
    color: #4db6ac !important;
    border: 1px solid rgba(0, 150, 136, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-violet {
    background-color: rgba(103, 58, 183, 0.15) !important;
    color: #9575cd !important;
    border: 1px solid rgba(103, 58, 183, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-yellow {
    background-color: rgba(255, 235, 59, 0.15) !important;
    color: #fff176 !important;
    border: 1px solid rgba(255, 235, 59, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-black {
    background-color: rgba(97, 97, 97, 0.15) !important;
    color: #9e9e9e !important;
    border: 1px solid rgba(97, 97, 97, 0.3) !important;
}
